"""
Flight Data Structure Module

This module defines unified data structures for storing flight parameters
with both readable values and correctly formatted ARINC 429 values.
"""

from dataclasses import dataclass
from typing import Dict, Any, Union


# ARINC 429 scaling parameters for different parameter types
ARINC_SCALING_PARAMS = {
    'latitude': {'min': -90.0, 'max': 90.0, 'bits': 20},           # Degrees
    'longitude': {'min': -180.0, 'max': 180.0, 'bits': 21},       # Degrees
    'altitude': {'min': -1000.0, 'max': 50000.0, 'bits': 17},     # Feet
    'ground_speed': {'min': 0.0, 'max': 1000.0, 'bits': 15},      # Knots
    'indicated_air_speed': {'min': 0.0, 'max': 1000.0, 'bits': 15}, # Knots
    'heading': {'min': 0.0, 'max': 360.0, 'bits': 15},            # Degrees
    'temperature': {'min': -80.0, 'max': 60.0, 'bits': 12},       # Celsius
    'eta_seconds': {'min': 0.0, 'max': 86400.0, 'bits': 17},      # Seconds (24 hours)
    'total_flight_time': {'min': 0.0, 'max': 86400.0, 'bits': 17}, # Seconds
    'distance_nm': {'min': 0.0, 'max': 10000.0, 'bits': 16},      # Nautical miles
    'progress': {'min': 0.0, 'max': 1.0, 'bits': 16},             # Ratio (0-1)
}


@dataclass
class ArincParameter:
    """
    Represents a single ARINC parameter with both readable and encoded values.
    
    Attributes:
        readable_value: Human-readable value (e.g., 35000.0 for altitude)
        arinc_value: ARINC 429 formatted value for PBA Pro labels
        unit: Unit of measurement (e.g., "ft", "knots", "degrees")
        description: Human-readable description
    """
    readable_value: Union[float, int, str]
    arinc_value: Union[float, int]
    unit: str = ""
    description: str = ""


@dataclass
class FlightDataSet:
    """
    Unified data structure containing all flight parameters and ARINC words.
    
    This structure eliminates redundant calculations by storing all values
    in one place with both readable and ARINC-formatted representations.
    """
    # Basic flight parameters
    latitude: ArincParameter
    longitude: ArincParameter
    ground_speed: ArincParameter
    heading: ArincParameter
    altitude: ArincParameter
    indicated_air_speed: ArincParameter
    flight_phase: ArincParameter
    eta_seconds: ArincParameter
    total_flight_time: ArincParameter
    distance_nm: ArincParameter
    progress: ArincParameter
    
    # Calculated parameters
    temperature: ArincParameter

    # Encoded flight information (ARINC 429 words) as individual parameters
    flight_nr_0: ArincParameter  # First 2 characters of flight number
    flight_nr_1: ArincParameter  # Characters 3-4 of flight number
    flight_nr_2: ArincParameter  # Characters 5-6 of flight number
    city_pair_0: ArincParameter  # First 3 characters of city pair
    city_pair_1: ArincParameter  # Characters 4-6 of city pair

    # Label mapping for PBA Pro
    label_values: Dict[str, Union[float, int]]
    
    @classmethod
    def create_from_flight_data(cls, 
                               flight_data: Dict[str, Any], 
                               flight_number: str, 
                               city_pair: str,
                               label_mapping: Dict[str, Any],
                               config_manager) -> 'FlightDataSet':
        """
        Create a FlightDataSet from raw flight data and configuration.
        
        Args:
            flight_data: Raw flight simulation data
            flight_number: Flight number for encoding
            city_pair: City pair for encoding
            label_mapping: Label configuration mapping
            config_manager: Configuration manager instance
            
        Returns:
            FlightDataSet with all values calculated once
        """
        from ..arinc.utilities import encode_flight_info, scale_arinc_value
        
        # Calculate temperature once
        temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2
        
        # Generate ARINC words once
        arinc_words = encode_flight_info(flight_number, city_pair)
        
        # Create ArincParameter objects for all flight data with proper scaling
        lat_params = ARINC_SCALING_PARAMS['latitude']
        latitude = ArincParameter(
            readable_value=flight_data["Latitude_of_aircraft"],
            arinc_value=scale_arinc_value(
                flight_data["Latitude_of_aircraft"],
                lat_params['min'], lat_params['max'], lat_params['bits']
            ),
            unit="degrees",
            description="Aircraft Latitude"
        )

        lon_params = ARINC_SCALING_PARAMS['longitude']
        longitude = ArincParameter(
            readable_value=flight_data["Longitude_of_aircraft"],
            arinc_value=scale_arinc_value(
                flight_data["Longitude_of_aircraft"],
                lon_params['min'], lon_params['max'], lon_params['bits']
            ),
            unit="degrees",
            description="Aircraft Longitude"
        )

        gs_params = ARINC_SCALING_PARAMS['ground_speed']
        ground_speed = ArincParameter(
            readable_value=flight_data["Ground_Speed_knots"],
            arinc_value=scale_arinc_value(
                flight_data["Ground_Speed_knots"],
                gs_params['min'], gs_params['max'], gs_params['bits']
            ),
            unit="knots",
            description="Ground Speed"
        )

        hdg_params = ARINC_SCALING_PARAMS['heading']
        heading = ArincParameter(
            readable_value=flight_data["Heading_degrees"],
            arinc_value=scale_arinc_value(
                flight_data["Heading_degrees"],
                hdg_params['min'], hdg_params['max'], hdg_params['bits']
            ),
            unit="degrees",
            description="Aircraft Heading"
        )
        
        alt_params = ARINC_SCALING_PARAMS['altitude']
        altitude = ArincParameter(
            readable_value=flight_data["Altitude_of_aircraft_ft"],
            arinc_value=scale_arinc_value(
                flight_data["Altitude_of_aircraft_ft"],
                alt_params['min'], alt_params['max'], alt_params['bits']
            ),
            unit="ft",
            description="Aircraft Altitude"
        )

        ias_params = ARINC_SCALING_PARAMS['indicated_air_speed']
        indicated_air_speed = ArincParameter(
            readable_value=flight_data["Indicated_Air_Speed_knots"],
            arinc_value=scale_arinc_value(
                flight_data["Indicated_Air_Speed_knots"],
                ias_params['min'], ias_params['max'], ias_params['bits']
            ),
            unit="knots",
            description="Indicated Air Speed"
        )

        # Flight phase is a string, so we'll use a simple hash for ARINC value
        flight_phase = ArincParameter(
            readable_value=flight_data["Flight_Phase"],
            arinc_value=hash(flight_data["Flight_Phase"]) & 0xFFFF,  # 16-bit hash
            unit="",
            description="Flight Phase"
        )

        eta_params = ARINC_SCALING_PARAMS['eta_seconds']
        eta_seconds = ArincParameter(
            readable_value=flight_data["ETA_seconds"],
            arinc_value=scale_arinc_value(
                flight_data["ETA_seconds"],
                eta_params['min'], eta_params['max'], eta_params['bits']
            ),
            unit="seconds",
            description="Estimated Time of Arrival"
        )

        tft_params = ARINC_SCALING_PARAMS['total_flight_time']
        total_flight_time = ArincParameter(
            readable_value=flight_data["Total_Flight_Time_seconds"],
            arinc_value=scale_arinc_value(
                flight_data["Total_Flight_Time_seconds"],
                tft_params['min'], tft_params['max'], tft_params['bits']
            ),
            unit="seconds",
            description="Total Flight Time"
        )

        dist_params = ARINC_SCALING_PARAMS['distance_nm']
        distance_nm = ArincParameter(
            readable_value=flight_data["Distance_nm"],
            arinc_value=scale_arinc_value(
                flight_data["Distance_nm"],
                dist_params['min'], dist_params['max'], dist_params['bits']
            ),
            unit="nm",
            description="Distance to Destination"
        )
        
        prog_params = ARINC_SCALING_PARAMS['progress']
        progress = ArincParameter(
            readable_value=flight_data["progress"],
            arinc_value=scale_arinc_value(
                flight_data["progress"],
                prog_params['min'], prog_params['max'], prog_params['bits']
            ),
            unit="",
            description="Flight Progress"
        )

        temp_params = ARINC_SCALING_PARAMS['temperature']
        temperature = ArincParameter(
            readable_value=temperature_value,
            arinc_value=scale_arinc_value(
                temperature_value,
                temp_params['min'], temp_params['max'], temp_params['bits']
            ),
            unit="°C",
            description="Static Air Temperature"
        )

        # Create individual ArincParameter objects for flight info words
        # Parse the flight number and city pair from the encoded words
        flight_number_padded = flight_number.ljust(6)
        city_pair_padded = city_pair.ljust(6)

        # Extract individual ARINC words and create parameters
        flight_info_params = {}
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                index = int(key.split(":")[0].replace("FlightNr", ""))
                chars = flight_number_padded[index*2:(index*2)+2]
                flight_info_params[f"flight_nr_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"Flight Number Part {index} ({chars.strip()})"
                )
            elif "CityPair" in key:
                index = int(key.split(":")[0].replace("CityPair", ""))
                chars = city_pair_padded[index*3:(index*3)+3]
                flight_info_params[f"city_pair_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"City Pair Part {index} ({chars.strip()})"
                )

        # Create extended flight data for label mapping
        flight_data_with_extras = flight_data.copy()
        flight_data_with_extras["Static_Air_Temperature"] = temperature_value
        
        # Add encoded flight info values
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                flight_data_with_extras[f"FlightNr{flight_nr_index}"] = value
            elif "CityPair" in key:
                city_pair_index = key.split(":")[0].replace("CityPair", "")
                flight_data_with_extras[f"CityPair{city_pair_index}"] = value
        
        # Generate label values for PBA Pro
        label_values = {}
        for label_name, (_, _, simulated_value) in label_mapping.items():
            flight_data_key = config_manager.get_flight_data_key(simulated_value)
            if flight_data_key in flight_data_with_extras:
                label_values[label_name] = flight_data_with_extras[flight_data_key]
            else:
                label_values[label_name] = 0.0
        
        return cls(
            latitude=latitude,
            longitude=longitude,
            ground_speed=ground_speed,
            heading=heading,
            altitude=altitude,
            indicated_air_speed=indicated_air_speed,
            flight_phase=flight_phase,
            eta_seconds=eta_seconds,
            total_flight_time=total_flight_time,
            distance_nm=distance_nm,
            progress=progress,
            temperature=temperature,
            flight_nr_0=flight_info_params.get('flight_nr_0', ArincParameter("", 0, "", "Flight Number Part 0")),
            flight_nr_1=flight_info_params.get('flight_nr_1', ArincParameter("", 0, "", "Flight Number Part 1")),
            flight_nr_2=flight_info_params.get('flight_nr_2', ArincParameter("", 0, "", "Flight Number Part 2")),
            city_pair_0=flight_info_params.get('city_pair_0', ArincParameter("", 0, "", "City Pair Part 0")),
            city_pair_1=flight_info_params.get('city_pair_1', ArincParameter("", 0, "", "City Pair Part 1")),
            label_values=label_values
        )
    
    def get_parameter_by_name(self, name: str) -> ArincParameter:
        """Get a parameter by its name."""
        parameter_map = {
            'latitude': self.latitude,
            'longitude': self.longitude,
            'ground_speed': self.ground_speed,
            'heading': self.heading,
            'altitude': self.altitude,
            'indicated_air_speed': self.indicated_air_speed,
            'flight_phase': self.flight_phase,
            'eta_seconds': self.eta_seconds,
            'total_flight_time': self.total_flight_time,
            'distance_nm': self.distance_nm,
            'progress': self.progress,
            'temperature': self.temperature,
            'flight_nr_0': self.flight_nr_0,
            'flight_nr_1': self.flight_nr_1,
            'flight_nr_2': self.flight_nr_2,
            'city_pair_0': self.city_pair_0,
            'city_pair_1': self.city_pair_1
        }
        return parameter_map.get(name.lower())

    def get_flight_info_words_dict(self) -> Dict[str, int]:
        """Get flight info words as a dictionary for backward compatibility."""
        return {
            f"FlightNr0: {self.flight_nr_0.readable_value}": self.flight_nr_0.arinc_value,
            f"FlightNr1: {self.flight_nr_1.readable_value}": self.flight_nr_1.arinc_value,
            f"FlightNr2: {self.flight_nr_2.readable_value}": self.flight_nr_2.arinc_value,
            f"CityPair0: {self.city_pair_0.readable_value}": self.city_pair_0.arinc_value,
            f"CityPair1: {self.city_pair_1.readable_value}": self.city_pair_1.arinc_value
        }
