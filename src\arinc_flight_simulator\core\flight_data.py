"""
Flight Data Structure Module

This module defines unified data structures for storing flight parameters
with both readable values and correctly formatted ARINC 429 values.
"""

from dataclasses import dataclass
from typing import Dict, Any, Union


@dataclass
class ArincParameter:
    """
    Represents a single ARINC parameter with both readable and encoded values.
    
    Attributes:
        readable_value: Human-readable value (e.g., 35000.0 for altitude)
        arinc_value: ARINC 429 formatted value for PBA Pro labels
        unit: Unit of measurement (e.g., "ft", "knots", "degrees")
        description: Human-readable description
    """
    readable_value: Union[float, int, str]
    arinc_value: Union[float, int]
    unit: str = ""
    description: str = ""


@dataclass
class FlightDataSet:
    """
    Unified data structure containing all flight parameters and ARINC words.
    
    This structure eliminates redundant calculations by storing all values
    in one place with both readable and ARINC-formatted representations.
    """
    # Basic flight parameters
    latitude: ArincParameter
    longitude: ArincParameter
    ground_speed: ArincParameter
    heading: ArincParameter
    altitude: ArincParameter
    indicated_air_speed: ArincParameter
    flight_phase: ArincParameter
    eta_seconds: ArincParameter
    total_flight_time: ArincParameter
    distance_nm: ArincParameter
    progress: ArincParameter
    
    # Calculated parameters
    temperature: ArincParameter
    
    # Encoded flight information (ARINC 429 words)
    flight_info_words: Dict[str, int]
    
    # Label mapping for PBA Pro
    label_values: Dict[str, Union[float, int]]
    
    @classmethod
    def create_from_flight_data(cls, 
                               flight_data: Dict[str, Any], 
                               flight_number: str, 
                               city_pair: str,
                               label_mapping: Dict[str, Any],
                               config_manager) -> 'FlightDataSet':
        """
        Create a FlightDataSet from raw flight data and configuration.
        
        Args:
            flight_data: Raw flight simulation data
            flight_number: Flight number for encoding
            city_pair: City pair for encoding
            label_mapping: Label configuration mapping
            config_manager: Configuration manager instance
            
        Returns:
            FlightDataSet with all values calculated once
        """
        from ..arinc.utilities import encode_flight_info
        
        # Calculate temperature once
        temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2
        
        # Generate ARINC words once
        arinc_words = encode_flight_info(flight_number, city_pair)
        
        # Create ArincParameter objects for all flight data
        latitude = ArincParameter(
            readable_value=flight_data["Latitude_of_aircraft"],
            arinc_value=flight_data["Latitude_of_aircraft"],
            unit="degrees",
            description="Aircraft Latitude"
        )
        
        longitude = ArincParameter(
            readable_value=flight_data["Longitude_of_aircraft"],
            arinc_value=flight_data["Longitude_of_aircraft"],
            unit="degrees",
            description="Aircraft Longitude"
        )
        
        ground_speed = ArincParameter(
            readable_value=flight_data["Ground_Speed_knots"],
            arinc_value=flight_data["Ground_Speed_knots"],
            unit="knots",
            description="Ground Speed"
        )
        
        heading = ArincParameter(
            readable_value=flight_data["Heading_degrees"],
            arinc_value=flight_data["Heading_degrees"],
            unit="degrees",
            description="Aircraft Heading"
        )
        
        altitude = ArincParameter(
            readable_value=flight_data["Altitude_of_aircraft_ft"],
            arinc_value=flight_data["Altitude_of_aircraft_ft"],
            unit="ft",
            description="Aircraft Altitude"
        )
        
        indicated_air_speed = ArincParameter(
            readable_value=flight_data["Indicated_Air_Speed_knots"],
            arinc_value=flight_data["Indicated_Air_Speed_knots"],
            unit="knots",
            description="Indicated Air Speed"
        )
        
        flight_phase = ArincParameter(
            readable_value=flight_data["Flight_Phase"],
            arinc_value=flight_data["Flight_Phase"],
            unit="",
            description="Flight Phase"
        )
        
        eta_seconds = ArincParameter(
            readable_value=flight_data["ETA_seconds"],
            arinc_value=flight_data["ETA_seconds"],
            unit="seconds",
            description="Estimated Time of Arrival"
        )
        
        total_flight_time = ArincParameter(
            readable_value=flight_data["Total_Flight_Time_seconds"],
            arinc_value=flight_data["Total_Flight_Time_seconds"],
            unit="seconds",
            description="Total Flight Time"
        )
        
        distance_nm = ArincParameter(
            readable_value=flight_data["Distance_nm"],
            arinc_value=flight_data["Distance_nm"],
            unit="nm",
            description="Distance to Destination"
        )
        
        progress = ArincParameter(
            readable_value=flight_data["progress"],
            arinc_value=flight_data["progress"],
            unit="",
            description="Flight Progress"
        )
        
        temperature = ArincParameter(
            readable_value=temperature_value,
            arinc_value=temperature_value,
            unit="°C",
            description="Static Air Temperature"
        )
        
        # Create extended flight data for label mapping
        flight_data_with_extras = flight_data.copy()
        flight_data_with_extras["Static_Air_Temperature"] = temperature_value
        
        # Add encoded flight info values
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                flight_data_with_extras[f"FlightNr{flight_nr_index}"] = value
            elif "CityPair" in key:
                city_pair_index = key.split(":")[0].replace("CityPair", "")
                flight_data_with_extras[f"CityPair{city_pair_index}"] = value
        
        # Generate label values for PBA Pro
        label_values = {}
        for label_name, (_, _, simulated_value) in label_mapping.items():
            flight_data_key = config_manager.get_flight_data_key(simulated_value)
            if flight_data_key in flight_data_with_extras:
                label_values[label_name] = flight_data_with_extras[flight_data_key]
            else:
                label_values[label_name] = 0.0
        
        return cls(
            latitude=latitude,
            longitude=longitude,
            ground_speed=ground_speed,
            heading=heading,
            altitude=altitude,
            indicated_air_speed=indicated_air_speed,
            flight_phase=flight_phase,
            eta_seconds=eta_seconds,
            total_flight_time=total_flight_time,
            distance_nm=distance_nm,
            progress=progress,
            temperature=temperature,
            flight_info_words=arinc_words,
            label_values=label_values
        )
    
    def get_parameter_by_name(self, name: str) -> ArincParameter:
        """Get a parameter by its name."""
        parameter_map = {
            'latitude': self.latitude,
            'longitude': self.longitude,
            'ground_speed': self.ground_speed,
            'heading': self.heading,
            'altitude': self.altitude,
            'indicated_air_speed': self.indicated_air_speed,
            'flight_phase': self.flight_phase,
            'eta_seconds': self.eta_seconds,
            'total_flight_time': self.total_flight_time,
            'distance_nm': self.distance_nm,
            'progress': self.progress,
            'temperature': self.temperature
        }
        return parameter_map.get(name.lower())
