"""
Flight Data Structure Module

This module defines unified data structures for storing flight parameters
with both readable values and correctly formatted ARINC 429 values.
"""

from dataclasses import dataclass
from typing import Dict, Any, Union


@dataclass
class ArincParameter:
    """
    Represents a single ARINC parameter with both readable and encoded values.
    
    Attributes:
        readable_value: Human-readable value (e.g., 35000.0 for altitude)
        arinc_value: ARINC 429 formatted value for PBA Pro labels
        unit: Unit of measurement (e.g., "ft", "knots", "degrees")
        description: Human-readable description
    """
    readable_value: Union[float, int, str]
    arinc_value: Union[float, int]
    unit: str = ""
    description: str = ""


@dataclass
class FlightDataSet:
    """
    Unified data structure containing all flight parameters and ARINC words.
    
    This structure eliminates redundant calculations by storing all values
    in one place with both readable and ARINC-formatted representations.
    """
    # Basic flight parameters
    latitude: ArincParameter
    longitude: ArincParameter
    ground_speed: ArincParameter
    heading: ArincParameter
    altitude: ArincParameter
    indicated_air_speed: ArincParameter
    flight_phase: ArincParameter
    eta_seconds: ArincParameter
    total_flight_time: ArincParameter
    distance_nm: ArincParameter
    progress: ArincParameter
    
    # Calculated parameters
    temperature: ArincParameter

    # Encoded flight information (ARINC 429 words) as individual parameters
    flight_nr_0: ArincParameter  # First 2 characters of flight number
    flight_nr_1: ArincParameter  # Characters 3-4 of flight number
    flight_nr_2: ArincParameter  # Characters 5-6 of flight number
    city_pair_0: ArincParameter  # First 3 characters of city pair
    city_pair_1: ArincParameter  # Characters 4-6 of city pair

    # Label mapping for PBA Pro
    label_values: Dict[str, Union[float, int]]
    
    @classmethod
    def create_from_flight_data(cls, 
                               flight_data: Dict[str, Any], 
                               flight_number: str, 
                               city_pair: str,
                               label_mapping: Dict[str, Any],
                               config_manager) -> 'FlightDataSet':
        """
        Create a FlightDataSet from raw flight data and configuration.
        
        Args:
            flight_data: Raw flight simulation data
            flight_number: Flight number for encoding
            city_pair: City pair for encoding
            label_mapping: Label configuration mapping
            config_manager: Configuration manager instance
            
        Returns:
            FlightDataSet with all values calculated once
        """
        from ..arinc.utilities import encode_flight_info
        
        # Calculate temperature once
        temperature_value = 15.0 - (flight_data["Altitude_of_aircraft_ft"] / 1000) * 2
        
        # Generate ARINC words once
        arinc_words = encode_flight_info(flight_number, city_pair)
        
        # Create ArincParameter objects for all flight data
        latitude = ArincParameter(
            readable_value=flight_data["Latitude_of_aircraft"],
            arinc_value=flight_data["Latitude_of_aircraft"],
            unit="degrees",
            description="Aircraft Latitude"
        )
        
        longitude = ArincParameter(
            readable_value=flight_data["Longitude_of_aircraft"],
            arinc_value=flight_data["Longitude_of_aircraft"],
            unit="degrees",
            description="Aircraft Longitude"
        )
        
        ground_speed = ArincParameter(
            readable_value=flight_data["Ground_Speed_knots"],
            arinc_value=flight_data["Ground_Speed_knots"],
            unit="knots",
            description="Ground Speed"
        )
        
        heading = ArincParameter(
            readable_value=flight_data["Heading_degrees"],
            arinc_value=flight_data["Heading_degrees"],
            unit="degrees",
            description="Aircraft Heading"
        )
        
        altitude = ArincParameter(
            readable_value=flight_data["Altitude_of_aircraft_ft"],
            arinc_value=flight_data["Altitude_of_aircraft_ft"],
            unit="ft",
            description="Aircraft Altitude"
        )
        
        indicated_air_speed = ArincParameter(
            readable_value=flight_data["Indicated_Air_Speed_knots"],
            arinc_value=flight_data["Indicated_Air_Speed_knots"],
            unit="knots",
            description="Indicated Air Speed"
        )
        
        flight_phase = ArincParameter(
            readable_value=flight_data["Flight_Phase"],
            arinc_value=flight_data["Flight_Phase"],
            unit="",
            description="Flight Phase"
        )
        
        eta_seconds = ArincParameter(
            readable_value=flight_data["ETA_seconds"],
            arinc_value=flight_data["ETA_seconds"],
            unit="seconds",
            description="Estimated Time of Arrival"
        )
        
        total_flight_time = ArincParameter(
            readable_value=flight_data["Total_Flight_Time_seconds"],
            arinc_value=flight_data["Total_Flight_Time_seconds"],
            unit="seconds",
            description="Total Flight Time"
        )
        
        distance_nm = ArincParameter(
            readable_value=flight_data["Distance_nm"],
            arinc_value=flight_data["Distance_nm"],
            unit="nm",
            description="Distance to Destination"
        )
        
        progress = ArincParameter(
            readable_value=flight_data["progress"],
            arinc_value=flight_data["progress"],
            unit="",
            description="Flight Progress"
        )
        
        temperature = ArincParameter(
            readable_value=temperature_value,
            arinc_value=temperature_value,
            unit="°C",
            description="Static Air Temperature"
        )

        # Create individual ArincParameter objects for flight info words
        # Parse the flight number and city pair from the encoded words
        flight_number_padded = flight_number.ljust(6)
        city_pair_padded = city_pair.ljust(6)

        # Extract individual ARINC words and create parameters
        flight_info_params = {}
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                index = int(key.split(":")[0].replace("FlightNr", ""))
                chars = flight_number_padded[index*2:(index*2)+2]
                flight_info_params[f"flight_nr_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"Flight Number Part {index} ({chars.strip()})"
                )
            elif "CityPair" in key:
                index = int(key.split(":")[0].replace("CityPair", ""))
                chars = city_pair_padded[index*3:(index*3)+3]
                flight_info_params[f"city_pair_{index}"] = ArincParameter(
                    readable_value=chars.strip(),
                    arinc_value=value,
                    unit="",
                    description=f"City Pair Part {index} ({chars.strip()})"
                )

        # Create extended flight data for label mapping
        flight_data_with_extras = flight_data.copy()
        flight_data_with_extras["Static_Air_Temperature"] = temperature_value
        
        # Add encoded flight info values
        for key, value in arinc_words.items():
            if "FlightNr" in key:
                flight_nr_index = key.split(":")[0].replace("FlightNr", "")
                flight_data_with_extras[f"FlightNr{flight_nr_index}"] = value
            elif "CityPair" in key:
                city_pair_index = key.split(":")[0].replace("CityPair", "")
                flight_data_with_extras[f"CityPair{city_pair_index}"] = value
        
        # Generate label values for PBA Pro
        label_values = {}
        for label_name, (_, _, simulated_value) in label_mapping.items():
            flight_data_key = config_manager.get_flight_data_key(simulated_value)
            if flight_data_key in flight_data_with_extras:
                label_values[label_name] = flight_data_with_extras[flight_data_key]
            else:
                label_values[label_name] = 0.0
        
        return cls(
            latitude=latitude,
            longitude=longitude,
            ground_speed=ground_speed,
            heading=heading,
            altitude=altitude,
            indicated_air_speed=indicated_air_speed,
            flight_phase=flight_phase,
            eta_seconds=eta_seconds,
            total_flight_time=total_flight_time,
            distance_nm=distance_nm,
            progress=progress,
            temperature=temperature,
            flight_nr_0=flight_info_params.get('flight_nr_0', ArincParameter("", 0, "", "Flight Number Part 0")),
            flight_nr_1=flight_info_params.get('flight_nr_1', ArincParameter("", 0, "", "Flight Number Part 1")),
            flight_nr_2=flight_info_params.get('flight_nr_2', ArincParameter("", 0, "", "Flight Number Part 2")),
            city_pair_0=flight_info_params.get('city_pair_0', ArincParameter("", 0, "", "City Pair Part 0")),
            city_pair_1=flight_info_params.get('city_pair_1', ArincParameter("", 0, "", "City Pair Part 1")),
            label_values=label_values
        )
    
    def get_parameter_by_name(self, name: str) -> ArincParameter:
        """Get a parameter by its name."""
        parameter_map = {
            'latitude': self.latitude,
            'longitude': self.longitude,
            'ground_speed': self.ground_speed,
            'heading': self.heading,
            'altitude': self.altitude,
            'indicated_air_speed': self.indicated_air_speed,
            'flight_phase': self.flight_phase,
            'eta_seconds': self.eta_seconds,
            'total_flight_time': self.total_flight_time,
            'distance_nm': self.distance_nm,
            'progress': self.progress,
            'temperature': self.temperature,
            'flight_nr_0': self.flight_nr_0,
            'flight_nr_1': self.flight_nr_1,
            'flight_nr_2': self.flight_nr_2,
            'city_pair_0': self.city_pair_0,
            'city_pair_1': self.city_pair_1
        }
        return parameter_map.get(name.lower())

    def get_flight_info_words_dict(self) -> Dict[str, int]:
        """Get flight info words as a dictionary for backward compatibility."""
        return {
            f"FlightNr0: {self.flight_nr_0.readable_value}": self.flight_nr_0.arinc_value,
            f"FlightNr1: {self.flight_nr_1.readable_value}": self.flight_nr_1.arinc_value,
            f"FlightNr2: {self.flight_nr_2.readable_value}": self.flight_nr_2.arinc_value,
            f"CityPair0: {self.city_pair_0.readable_value}": self.city_pair_0.arinc_value,
            f"CityPair1: {self.city_pair_1.readable_value}": self.city_pair_1.arinc_value
        }
