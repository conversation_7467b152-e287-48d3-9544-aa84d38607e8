"""
ARINC 429 Encoding Utilities

This module provides functions to encode flight information into ARINC 429 words.
ARINC 429 is a data transmission standard for aircraft avionics.
Includes BCD and BNR encoding functions for proper ARINC 429 data formatting.
"""

from typing import Dict, Union
import math


def scale_arinc_value(value: float, min_value: float, max_value: float, significant_bits: int) -> int:
    """
    Scale a numeric value to ARINC 429 format using the specified range and bit resolution.

    Args:
        value: The original value to scale
        min_value: Minimum value of the input range
        max_value: Maximum value of the input range
        significant_bits: Number of significant bits (determines output range)

    Returns:
        Scaled integer value where:
        - min_value maps to 0
        - max_value maps to (2^significant_bits - 1)
        - value is scaled proportionally

    Example:
        scale_arinc_value(25000.0, 0.0, 50000.0, 16)
        # Returns 32768 (25000 is halfway between 0-50000, maps to halfway of 0-65535)
    """
    if max_value <= min_value:
        raise ValueError("max_value must be greater than min_value")

    if significant_bits <= 0 or significant_bits > 32:
        raise ValueError("significant_bits must be between 1 and 32")

    # Calculate the maximum value for the given number of significant bits
    max_scaled = (1 << significant_bits) - 1  # 2^significant_bits - 1

    # Clamp the input value to the specified range
    clamped_value = max(min_value, min(max_value, value))

    # Scale the value from [min_value, max_value] to [0, max_scaled]
    normalized = (clamped_value - min_value) / (max_value - min_value)
    scaled_value = int(normalized * max_scaled)

    return scaled_value


def encode_bcd_arinc(value: float, sig_bits: int, resolution: float = 1.0,
                     sdi: int = 0) -> int:
    """
    Encode a value into ARINC 429 BCD format (data portion only).

    ARINC 429 Data Structure:
    | SSM(2) | Data(19) | SDI(2) |

    Args:
        value: The numeric value to encode
        sig_bits: Number of significant decimal digits
        resolution: Resolution multiplier (default 1.0)
        sdi: Source Destination Identifier (2 bits, default 0)

    Returns:
        23-bit data portion (SSM + Data + SDI) for ARINC 429 word
    """
    # Determine sign from value
    positive = value >= 0

    # Scale value by resolution
    scaled_value = int(abs(value) / resolution)

    # Convert to BCD digits
    digits = []
    temp_value = scaled_value
    for i in range(sig_bits):
        digits.append(temp_value % 10)
        temp_value //= 10

    # Encode digits in BCD format
    # First digit (most significant) uses 3 bits, others use 4 bits
    data = 0
    bit_pos = 0

    for i, digit in enumerate(digits):
        if i == sig_bits - 1:  # Most significant digit (3 bits)
            data |= (digit & 0x7) << bit_pos
            bit_pos += 3
        else:  # Other digits (4 bits)
            data |= (digit & 0xF) << bit_pos
            bit_pos += 4

    # Build data portion (without label)
    data_word = 0
    data_word |= (sdi & 0x3)  # Bits 1-2: SDI
    data_word |= (data & 0x7FFFF) << 2  # Bits 3-21: Data (19 bits)

    # SSM for BCD (bits 22-23)
    if positive:
        ssm = 0b00  # Plus, North, East, Right, To, Above
    else:
        ssm = 0b11  # Minus, South, West, Left, From, Below
    data_word |= (ssm & 0x3) << 21

    return data_word


def encode_bnr_arinc(value: float, range_max: float, sig_bits: int = 20,
                     sdi: int = 0) -> int:
    """
    Encode a value into ARINC 429 BNR (Binary) format (data portion only).

    ARINC 429 Data Structure:
    | SSM(3) | Data(18) | SDI(2) | for sig_bits <= 18
    | SSM(3) | Data(20) |         for sig_bits > 18

    Args:
        value: The numeric value to encode
        range_max: Maximum value of the range (minimum is 0)
        sig_bits: Number of significant bits for data (default 20)
        sdi: Source Destination Identifier (2 bits, default 0)

    Returns:
        21-23 bit data portion (SSM + Data + SDI) for ARINC 429 word
    """
    # Determine sign from value
    positive = value >= 0

    # Clamp value to range
    abs_value = abs(value)
    if abs_value > range_max:
        abs_value = range_max

    # Calculate data bits using binary representation
    # MSB represents half of range_max, next bit is half of that, etc.
    data = 0
    remaining_value = abs_value

    for bit in range(sig_bits - 1, -1, -1):
        bit_value = range_max / (1 << (sig_bits - 1 - bit))
        if remaining_value >= bit_value:
            data |= (1 << bit)
            remaining_value -= bit_value

    # Build data portion (without label)
    data_word = 0

    if sig_bits <= 18:
        # Standard format with SDI
        data_word |= (sdi & 0x3)  # Bits 1-2: SDI
        data_word |= (data & ((1 << sig_bits) - 1)) << 2  # Data starts at bit 3

        # SSM for BNR (bits 21-23)
        if positive:
            ssm = 0b111  # Normal Operation, positive
        else:
            ssm = 0b100  # Normal Operation, negative (sign bit set)
        data_word |= (ssm & 0x7) << (2 + sig_bits)
    else:
        # Extended format (sig_bits > 18), SDI becomes part of data
        data_word |= (data & ((1 << sig_bits) - 1))  # Data starts at bit 1

        # SSM for BNR (bits 21-23)
        if positive:
            ssm = 0b111  # Normal Operation, positive
        else:
            ssm = 0b100  # Normal Operation, negative (sign bit set)
        data_word |= (ssm & 0x7) << sig_bits

    return data_word


def encode_char(char: str) -> int:
    """
    Encode a single character into 7-bit ASCII.
    
    Args:
        char: Character to encode
        
    Returns:
        7-bit ASCII value, or space character for unsupported characters
    """
    ascii_val = ord(char)
    if 32 <= ascii_val <= 126:
        return ascii_val & 0x7F  # Ensure 7-bit
    else:
        return ord(' ')  # Replace unsupported characters with space


def encode_char2(char1: str, char2: str) -> int:
    """
    Encode two characters into an ARINC 429 word with Char 2 format.
    
    Args:
        char1: First character
        char2: Second character
        
    Returns:
        ARINC 429 word containing both characters
    """
    c1 = encode_char(char1)
    c2 = encode_char(char2)
    word = 0

    word |= (c1 & 0x7F) << 2
    word |= (c2 & 0x7F) << 10    

    return word


def encode_char3(char1: str, char2: str, char3: str) -> int:
    """
    Encode three characters into an ARINC 429 word with Char 3 format.
    
    Args:
        char1: First character
        char2: Second character
        char3: Third character
        
    Returns:
        ARINC 429 word containing all three characters
    """
    c1 = encode_char(char1)
    c2 = encode_char(char2)
    c3 = encode_char(char3)
    word = 0
    
    word |= (c1 & 0x7F)     
    word |= (c2 & 0x7F) << 7     
    word |= (c3 & 0x7F) << 14     
    
    return word


def encode_flight_info(flight_number: str, city_pair: str) -> Dict[str, int]:
    """
    Encode flight number and city pair into ARINC 429 words.
    
    Flight number is encoded using Char 2 format (2 characters per word).
    City pair is encoded using Char 3 format (3 characters per word).
    
    Args:
        flight_number: Flight number string (e.g., "LH441")
        city_pair: City pair string (e.g., "FRANYC")
        
    Returns:
        Dictionary mapping description strings to ARINC 429 words
    """
    arinc_words = {}

    # Encode flight number (Char 2 format)
    flight_number = flight_number.ljust(6)  # Pad to 6 characters
    for i in range(0, 6, 2):
        word = encode_char2(flight_number[i], flight_number[i+1])
        arinc_words[f"FlightNr{str(int(i/2))}: {flight_number[i]}{flight_number[i+1]} "] = word

    # Encode city pair (Char 3 format)
    city_pair = city_pair.ljust(6)  # Pad to 6 characters
    for i in range(0, 6, 3):
        word = encode_char3(city_pair[i], city_pair[i+1], city_pair[i+2])
        arinc_words[f"CityPair{str(int(i/3))}: {city_pair[i]}{city_pair[i+1]}{city_pair[i+2]}"] = word

    return arinc_words
