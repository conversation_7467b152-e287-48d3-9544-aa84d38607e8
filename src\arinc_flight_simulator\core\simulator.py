"""
ARINC Flight Simulator - Core Simulator Module

This module contains the main ArincFlightSimulator class that orchestrates
the flight simulation, UI management, and ARINC 429 data generation.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import os
from typing import Optional

# Try to import PbaPro - if it fails, we'll run in simulation-only mode
try:
    from ppbase import *
    PBAPRO_AVAILABLE = True
except ImportError:
    PBAPRO_AVAILABLE = False

from ..ui.interface import create_ui
from .flight_model import simulate_flight_data
from .config import ConfigManager, ConfigDialog
from .flight_data import FlightDataSet


class ArincFlightSimulator:
    """
    Main ARINC Flight Simulator class.
    
    This class manages the flight simulation, UI interactions, and ARINC 429 data output.
    It supports both PbaPro hardware integration and simulation-only mode.
    """
    
    # ARINC labels that correspond to flight data - initialized from config
    AllLabels = []

    def __init__(self, config_file: Optional[str] = None):
        """Initialize the ARINC Flight Simulator."""
        self.running = False
        self.flight_thread: Optional[threading.Thread] = None
        self.time_since_takeoff = 0

        # Initialize configuration manager with provided config file path
        self.config_manager = ConfigManager(config_file)

        # Load configuration values
        self._load_configuration()

        # Initialize AllLabels from configuration
        self.update_labels_from_config()

        # PbaPro related variables
        self.pbapro_initialized = False
        self.labels = []

        # Create the UI first
        self.root = create_ui()

        # Set up window close protocol to properly stop simulation
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Initialize PbaPro if available
        self.init_pbapro()

        # Add simulation controls to the existing UI
        self.add_controls_to_ui()

    def _load_configuration(self) -> None:
        """Load configuration values from config manager."""
        self.update_interval = self.config_manager.get_float('GENERAL', 'update_interval')
        self.time_acceleration = self.config_manager.get_int('GENERAL', 'time_acceleration')
        self.pbapro_board_name = self.config_manager.get_value('GENERAL', 'pbapro_board_name')
        self.random_seed = self.config_manager.get_int('GENERAL', 'random_seed')
        self.movement_margin = self.config_manager.get_float('GENERAL', 'movement_margin')

    def run(self) -> None:
        """Start the GUI main loop."""
        self.root.mainloop()

    def on_closing(self) -> None:
        """Handle window closing event - stop simulation before closing."""
        if self.running:
            # Stop the simulation thread first
            self.running = False
            if self.flight_thread:
                self.flight_thread.join(timeout=2.0)  # Wait up to 2 seconds for thread to finish

        # Now it's safe to destroy the window
        self.root.destroy()

    def init_pbapro(self) -> None:
        """Initialize PbaPro ARINC system if available."""
        if not PBAPRO_AVAILABLE:
            print("PbaPro not available - running in simulation-only mode")
            return

        try:
            # Initialize PbaPro objects similar to LSY_Arinc_TestSetup
            self.A429Resource = PbaProObject(self.pbapro_board_name)
            self.GlobalConnections = PbaProObject("GlobalConnections")
            self.DBSPrjFile = PbaProObject("DBSPrjFile")

            # Initialize the system
            self.GlobalConnections.Reset()
            self.DBSPrjFile.Init()
            self.A429Resource.Init()

            # Set stream ID
            self.A429Resource.DbsStreamId = "ARINC_1"

            print("PbaPro basic initialization successful")

            # Setup basic ARINC configuration - only mark as initialized if this succeeds
            if self.setup_arinc_labels():
                self.pbapro_initialized = True
                print("PbaPro initialization completed successfully")
            else:
                print("PbaPro basic initialization succeeded but ARINC labels setup failed")

        except Exception as e:
            print(f"Failed to initialize PbaPro: {e}")
            self.pbapro_initialized = False

    def setup_arinc_labels(self) -> bool:
        """Setup ARINC labels for flight parameters.

        Returns:
            bool: True if setup was successful, False otherwise
        """
        if not PBAPRO_AVAILABLE:
            return False

        try:
            # Load database files
            current_dir = os.path.dirname(os.path.abspath(__file__))
            try:
                self.DBSPrjFile.AddFile(os.path.join(current_dir, "Combined.ppdbs"), 0, "")
                self.DBSPrjFile.LoadAll()
                print("Database file (Combined.ppdbs) loaded successfully")
            except Exception as e:
                print(f"Warning: Could not load Combined.ppdbs database file: {e}")
                print("Continuing without database file...")

            DBS = PbaProObject("Parameters")
            self.labels = []

            # Get channel configuration from config manager
            channel_config = self.config_manager.get_channel_config()

            # Get label to channel mapping from config manager
            label_channel_mapping = self.config_manager.get_label_mapping()

            # Setup channels first
            for channel_no, speed in channel_config.items():
                try:
                    channel = self.A429Resource.GetChannel(channel_no)
                    channel.ChannelType = "Tx"
                    channel.ChannelSpeed = speed
                    channel.DbsChannelId = 1
                    print(f"Configured Channel {channel_no} with speed {speed}")
                except Exception as e:
                    print(f"Failed to configure channel {channel_no}: {e}")

            # Create labels for each configured label
            for label_name in self.AllLabels:
                if label_name in label_channel_mapping:
                    channel_no, label_string, simulated_value = label_channel_mapping[label_name]
                    try:
                        channel = self.A429Resource.GetChannel(channel_no)

                        # Determine label number
                        if label_string == "x":
                            # Get label from database
                            try:
                                DBSParam = DBS.GetChild("*." + label_name)
                                label_no = DBSParam.parent().Label  # Label No decimal representation
                            except:
                                # Fallback to sequential numbering if database lookup fails
                                label_no = 100 + self.AllLabels.index(label_name)
                        else:
                            # Parse octal label number
                            label_no = int(label_string, 8)

                        # Create the label
                        label = channel.Send.Setup.LabelList.NewLabel(label_no)
                        label.objectName = label_name

                        # Store the original label for setting data
                        self.labels.append(label)

                        print(f"Created label for {label_name} on Channel {channel_no}, Label {label_no}")

                    except Exception as e:
                        print(f"Failed to create label for {label_name}: {e}")

            # Sync and enable transmission
            self.A429Resource.DBSSync()
            self.A429Resource.AllTxCtrl(True)

            print(f"Successfully created {len(self.labels)} ARINC labels")
            return True

        except Exception as e:
            print(f"Failed to setup ARINC labels: {e}")
            return False

    def add_controls_to_ui(self) -> None:
        """Add simulation controls to the UI."""
        # Add a frame for simulation controls (positioned directly after mainframe)
        control_frame = ttk.LabelFrame(self.root, text="Simulation Controls")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))

        # Start button
        self.start_button = ttk.Button(control_frame, text="Start Simulation", command=self.start_simulation)
        self.start_button.grid(row=0, column=0, padx=5, pady=5)

        # Stop button
        self.stop_button = ttk.Button(control_frame, text="Stop Simulation", command=self.stop_simulation)
        self.stop_button.grid(row=0, column=1, padx=5, pady=5)
        self.stop_button.config(state=tk.DISABLED)

        # Status label
        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(control_frame, textvariable=self.status_var).grid(row=0, column=2, padx=5, pady=5)

        # PbaPro status label
        pbapro_status = "PbaPro: Connected" if self.pbapro_initialized else "PbaPro: Simulation Only"
        self.pbapro_status_var = tk.StringVar(value=pbapro_status)
        pbapro_label = ttk.Label(control_frame, textvariable=self.pbapro_status_var)
        pbapro_label.grid(row=0, column=3, padx=5, pady=5)

        # Color code the PbaPro status
        if self.pbapro_initialized:
            pbapro_label.configure(foreground="green")
        else:
            pbapro_label.configure(foreground="orange")

        # Configuration button (top right)
        self.config_button = ttk.Button(control_frame, text="Configuration", command=self.show_config_dialog)
        self.config_button.grid(row=0, column=4, padx=5, pady=5)

        # Progress bar
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=1.0)
        self.progress_bar.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=5, pady=5)

        # Output frame (this is the only frame that should expand vertically)
        output_frame = ttk.LabelFrame(self.root, text="ARINC Data Output")
        output_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)

        # Text widget for output
        self.output_text = tk.Text(output_frame, height=15, width=60)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Scrollbar
        scrollbar = ttk.Scrollbar(output_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.output_text.configure(yscrollcommand=scrollbar.set)

        # Configure grid weights - only output frame (row 2) should expand vertically
        self.root.grid_rowconfigure(0, weight=0)  # Mainframe - fixed size
        self.root.grid_rowconfigure(1, weight=0)  # Control frame - fixed size
        self.root.grid_rowconfigure(2, weight=1)  # Output frame - expandable
        self.root.grid_columnconfigure(0, weight=1)  # Allow horizontal expansion

        # Configure output frame internal expansion
        output_frame.grid_rowconfigure(0, weight=1)
        output_frame.grid_columnconfigure(0, weight=1)

    def start_simulation(self) -> None:
        """Start the flight simulation."""
        # Import UI variables
        from ..ui import interface

        # Check if UI variables are available
        if not interface.start_var or not interface.dest_var or not interface.flight_var or not interface.daytime_var:
            messagebox.showerror("Error", "UI not properly initialized. Please restart the application.")
            return

        # Get input values
        start_airport_name = interface.start_var.get()
        dest_airport_name = interface.dest_var.get()
        flight_number = interface.flight_var.get()
        daytime = interface.daytime_var.get()

        # Validate airport selection
        if not start_airport_name or not dest_airport_name:
            messagebox.showerror("Error", "Please select both start and destination airports")
            return

        # Convert airport names to ICAO codes
        if not interface.name_to_icao:
            messagebox.showerror("Error", "Airport data not properly loaded. Please restart the application.")
            return

        start_icao = interface.name_to_icao.get(start_airport_name, "")
        dest_icao = interface.name_to_icao.get(dest_airport_name, "")

        if not start_icao or not dest_icao:
            messagebox.showerror("Error", "Please select valid airports")
            return

        # Validate that destination is different from start
        if start_icao == dest_icao:
            messagebox.showerror("Error", "Destination airport cannot be the same as start airport")
            return

        # Initialize simulation
        self.time_since_takeoff = 0
        self.progress_var.set(0)  # Reset progress bar
        self.start_airport = start_icao
        self.dest_airport = dest_icao
        self.flight_number = flight_number
        self.city_pair = f"{start_icao[:3]}{dest_icao[:3]}"  # Create city pair from ICAO codes

        # Update UI
        self.status_var.set("Initializing...")
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"Initializing simulation for flight {flight_number}\n")
        self.output_text.insert(tk.END, f"Route: {start_airport_name} ({start_icao}) → {dest_airport_name} ({dest_icao})\n")
        self.output_text.insert(tk.END, f"Departure time: {daytime}\n")
        self.output_text.insert(tk.END, f"Simulation time reset to: {self.time_since_takeoff} seconds\n\n")
        self.output_text.insert(tk.END, "Setting up ARINC labels...\n")
        self.output_text.see(tk.END)

        # Simulate ARINC board initialization (similar to LSY_Arinc_TestSetup)
        self.output_text.insert(tk.END, "Initializing ARINC board...\n")
        self.output_text.insert(tk.END, "Setting up channels...\n")
        self.output_text.insert(tk.END, "Configuring labels...\n")

        # Show PbaPro status in output
        if self.pbapro_initialized:
            self.output_text.insert(tk.END, f"PbaPro connected - {len(self.labels)} labels configured\n")
            self.output_text.insert(tk.END, "Data will be sent to PbaPro database\n\n")
        else:
            self.output_text.insert(tk.END, "PbaPro not available - simulation only mode\n")
            self.output_text.insert(tk.END, "Data will be displayed in UI only\n\n")

        self.output_text.see(tk.END)

        # Start simulation thread
        self.running = True
        self.flight_thread = threading.Thread(target=self.run_simulation)
        self.flight_thread.daemon = True
        self.flight_thread.start()

        # Update button states
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.config_button.config(state=tk.DISABLED)  # Disable config during simulation

    def stop_simulation(self) -> None:
        """Stop the flight simulation."""
        self.running = False
        if self.flight_thread:
            self.flight_thread.join(timeout=1.0)

        # Reset simulation state for next run
        self.time_since_takeoff = 0

        try:
            self.progress_var.set(0)  # Reset progress bar
            self.status_var.set("Stopped")
            self.output_text.insert(tk.END, "\nSimulation stopped\n")
            self.output_text.insert(tk.END, "Ready for next simulation\n")
            self.output_text.see(tk.END)

            # Update button states
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.config_button.config(state=tk.NORMAL)  # Re-enable config when stopped
        except tk.TclError:
            # Window is being closed, skip UI updates
            pass

    def run_simulation(self) -> None:
        """Main simulation loop."""
        try:
            self.status_var.set("Running")
        except tk.TclError:
            # Window is being closed, stop simulation
            self.running = False
            return

        while self.running:
            # Get raw flight data
            flight_data = simulate_flight_data(
                self.start_airport,
                self.dest_airport,
                self.time_since_takeoff,
                self.random_seed,
                self.movement_margin
            )

            # Update progress bar - check if window is still valid
            try:
                self.progress_var.set(flight_data["progress"])
            except tk.TclError:
                # Window is being closed, stop simulation
                self.running = False
                return

            # Generate all values once using the unified data structure
            label_mapping = self.config_manager.get_label_mapping()
            flight_dataset = FlightDataSet.create_from_flight_data(
                flight_data,
                self.flight_number,
                self.city_pair,
                label_mapping,
                self.config_manager
            )

            # Update displays and labels with pre-generated data
            self.update_arinc_data(flight_dataset)
            self.update_arinc_labels(flight_dataset)

            # Increment simulation time
            self.time_since_takeoff += int(self.update_interval * self.time_acceleration)

            # Check if flight is complete
            if flight_dataset.progress.readable_value >= 1.0:
                self.running = False
                try:
                    self.status_var.set("Completed")
                    self.output_text.insert(tk.END, "\nFlight completed\n")
                    self.output_text.see(tk.END)
                    self.start_button.config(state=tk.NORMAL)
                    self.stop_button.config(state=tk.DISABLED)
                    self.config_button.config(state=tk.NORMAL)  # Re-enable config when completed
                except tk.TclError:
                    # Window is being closed, just stop simulation
                    pass
                break

            time.sleep(self.update_interval)

    def update_arinc_data(self, flight_dataset: FlightDataSet) -> None:
        """Update the ARINC data display using pre-generated flight dataset."""
        try:
            # Clear output
            self.output_text.delete(1.0, tk.END)
        except tk.TclError:
            # Window is being closed, skip UI updates
            return

        try:
            # Display flight status using the unified data structure
            self.output_text.insert(tk.END, f"Flight: {self.flight_number} ({self.city_pair})\n")
            self.output_text.insert(tk.END, f"Phase: {flight_dataset.flight_phase.readable_value}\n")
            self.output_text.insert(tk.END, f"Progress: {flight_dataset.progress.readable_value*100:.1f}%\n")
            self.output_text.insert(tk.END, f"ETA: {int(flight_dataset.eta_seconds.readable_value)//60} minutes\n\n")

            # Display ARINC data that would be sent to the board
            self.output_text.insert(tk.END, "ARINC 429 Data Output:\n")
            self.output_text.insert(tk.END, "-" * 50 + "\n")

            # Flight info words (encoded in ARINC 429 format)
            self.output_text.insert(tk.END, "Encoded Flight Info (ARINC 429 Words):\n")

            # Display individual flight info parameters
            self.output_text.insert(tk.END, f"FlightNr0: {flight_dataset.flight_nr_0.readable_value} = {flight_dataset.flight_nr_0.arinc_value:06X}\n")
            self.output_text.insert(tk.END, f"FlightNr1: {flight_dataset.flight_nr_1.readable_value} = {flight_dataset.flight_nr_1.arinc_value:06X}\n")
            self.output_text.insert(tk.END, f"FlightNr2: {flight_dataset.flight_nr_2.readable_value} = {flight_dataset.flight_nr_2.arinc_value:06X}\n")
            self.output_text.insert(tk.END, f"CityPair0: {flight_dataset.city_pair_0.readable_value} = {flight_dataset.city_pair_0.arinc_value:06X}\n")
            self.output_text.insert(tk.END, f"CityPair1: {flight_dataset.city_pair_1.readable_value} = {flight_dataset.city_pair_1.arinc_value:06X}\n")

            # Flight parameters with scaled ARINC values
            self.output_text.insert(tk.END, "-" * 50 + "\n")
            self.output_text.insert(tk.END, "Flight Parameters (Readable -> Scaled ARINC):\n")

            # Display key flight parameters with both readable and scaled ARINC values
            self.output_text.insert(tk.END, f"Altitude: {flight_dataset.altitude.readable_value:.1f} ft -> {flight_dataset.altitude.arinc_value}\n")
            self.output_text.insert(tk.END, f"Latitude: {flight_dataset.latitude.readable_value:.6f}° -> {flight_dataset.latitude.arinc_value}\n")
            self.output_text.insert(tk.END, f"Longitude: {flight_dataset.longitude.readable_value:.6f}° -> {flight_dataset.longitude.arinc_value}\n")
            self.output_text.insert(tk.END, f"Ground Speed: {flight_dataset.ground_speed.readable_value:.1f} kts -> {flight_dataset.ground_speed.arinc_value}\n")
            self.output_text.insert(tk.END, f"Heading: {flight_dataset.heading.readable_value:.1f}° -> {flight_dataset.heading.arinc_value}\n")
            self.output_text.insert(tk.END, f"Temperature: {flight_dataset.temperature.readable_value:.1f}°C -> {flight_dataset.temperature.arinc_value}\n")
            self.output_text.insert(tk.END, f"Progress: {flight_dataset.progress.readable_value:.3f} -> {flight_dataset.progress.arinc_value}\n")

            # Flight label values (for PBA Pro labels)
            self.output_text.insert(tk.END, "-" * 50 + "\n")
            self.output_text.insert(tk.END, "PBA Pro Label Values:\n")
            for label_name, value in flight_dataset.label_values.items():
                self.output_text.insert(tk.END, f"{label_name}: {value:.6f}\n")

            self.output_text.see(tk.END)
        except tk.TclError:
            # Window is being closed, skip UI updates
            return

    def update_arinc_labels(self, flight_dataset: FlightDataSet) -> None:
        """Update actual ARINC labels with flight data if PbaPro is available."""
        if not self.pbapro_initialized or not self.labels:
            return

        try:
            # Update each label with its corresponding value from the pre-generated dataset
            for label in self.labels:
                label_name = label.objectName
                if label_name in flight_dataset.label_values:
                    try:
                        # Set the label value directly
                        value = flight_dataset.label_values[label_name]
                        label.LabelValue = int(abs(value))
                    except Exception as e:
                        print(f"Failed to update label {label_name}: {e}")

        except Exception as e:
            print(f"Error updating ARINC labels: {e}")

    def show_config_dialog(self) -> None:
        """Show configuration dialog."""
        try:
            # Store old values before showing dialog (since dialog modifies config_manager in memory)
            old_channel_config = self.config_manager.get_channel_config()
            old_label_mapping = self.config_manager.get_label_mapping()

            dialog = ConfigDialog(self.root, self.config_manager)
            result = dialog.show_dialog()

            if result:  # Configuration was saved
                # Apply configuration changes immediately
                self.apply_configuration_changes(old_channel_config, old_label_mapping)
                messagebox.showinfo("Configuration", "Configuration updated and applied successfully!\n\nNote: PbaPro hardware changes require restarting the simulator.")
        except Exception as e:
            messagebox.showerror("Configuration Error", f"Failed to open configuration dialog: {e}")

    def apply_configuration_changes(self, old_channel_config=None, old_label_mapping=None) -> None:
        """Apply configuration changes immediately to the running simulator.

        Args:
            old_channel_config: Previous channel configuration for change detection
            old_label_mapping: Previous label mapping for change detection
        """
        print("Applying configuration changes...")

        # Store old values for comparison
        old_update_interval = self.update_interval
        old_time_acceleration = self.time_acceleration
        old_random_seed = self.random_seed
        old_movement_margin = self.movement_margin
        old_board_name = self.pbapro_board_name

        # Use provided old values or get current ones (for backward compatibility)
        if old_channel_config is None:
            old_channel_config = self.config_manager.get_channel_config()
        if old_label_mapping is None:
            old_label_mapping = self.config_manager.get_label_mapping()

        # Reload configuration values from config manager
        self.config_manager.load_config()  # Reload from file
        self._load_configuration()  # Update instance variables

        # Report what changed
        changes = []
        if old_update_interval != self.update_interval:
            changes.append(f"Update interval: {old_update_interval}s → {self.update_interval}s")
        if old_time_acceleration != self.time_acceleration:
            changes.append(f"Time acceleration: {old_time_acceleration}x → {self.time_acceleration}x")
        if old_random_seed != self.random_seed:
            changes.append(f"Random seed: {old_random_seed} → {self.random_seed}")
        if old_movement_margin != self.movement_margin:
            changes.append(f"Movement margin: {old_movement_margin} → {self.movement_margin}")
        if old_board_name != self.pbapro_board_name:
            changes.append(f"PbaPro board: {old_board_name} → {self.pbapro_board_name}")

        # Check for channel configuration changes
        new_channel_config = self.config_manager.get_channel_config()
        channel_changes = old_channel_config != new_channel_config
        if channel_changes:
            changes.append("Channel configuration changed")

        # Log changes
        if changes:
            print("Configuration changes applied:")
            for change in changes:
                print(f"  - {change}")
        else:
            print("No configuration changes detected")

        # Check if PbaPro reinitialization is needed
        if old_board_name != self.pbapro_board_name and PBAPRO_AVAILABLE:
            print("PbaPro board name changed - restart simulator for hardware changes to take effect")

        # Update label configuration and check for changes
        label_changes = self.update_labels_from_config(old_label_mapping)

        # Update PBA Pro configuration if simulation is not running and changes were detected
        if not self.running:
            # Only reload PBA Pro if there were label or channel changes
            if label_changes or channel_changes:
                try:
                    # Reset PBA Pro environment and reinitialize labels with new configuration
                    if self.pbapro_initialized:
                        print("Resetting PBA Pro environment and updating ARINC configuration...")
                        self.init_pbapro()  # This resets the environment and recreates labels
                        if self.pbapro_initialized:
                            print("ARINC configuration updated successfully")
                        else:
                            print("Warning: Failed to update ARINC configuration")
                except Exception as e:
                    print(f"Warning: Failed to update ARINC configuration: {e}")
            else:
                print("No label or channel changes detected - PBA Pro configuration unchanged")
        else:
            if label_changes or channel_changes:
                print("Simulation is running - label/channel changes will apply to next simulation")
            else:
                print("No label or channel changes detected")

    def update_labels_from_config(self, old_label_mapping=None) -> bool:
        """Update the AllLabels list from current configuration.

        Args:
            old_label_mapping: Optional previous label mapping for detailed change detection

        Returns:
            bool: True if label changes were detected, False otherwise
        """
        # Get current label mapping from configuration
        label_mapping = self.config_manager.get_label_mapping()

        # Update AllLabels to match configured labels
        old_labels = self.AllLabels.copy()
        self.AllLabels = list(label_mapping.keys())

        # Check for label name changes (additions/removals)
        added_labels = set(self.AllLabels) - set(old_labels)
        removed_labels = set(old_labels) - set(self.AllLabels)

        # Check for label configuration changes (channel, label_id, simulated_value)
        config_changes = False
        if old_label_mapping is not None:
            for label_name in set(old_labels) & set(self.AllLabels):  # Common labels
                old_config = old_label_mapping.get(label_name)
                new_config = label_mapping.get(label_name)
                if old_config != new_config:
                    config_changes = True
                    print(f"Label '{label_name}' configuration changed: {old_config} → {new_config}")

        # Report changes
        if added_labels:
            print(f"Added labels: {', '.join(added_labels)}")
        if removed_labels:
            print(f"Removed labels: {', '.join(removed_labels)}")
        if not added_labels and not removed_labels and not config_changes:
            print("No label changes detected")

        return bool(added_labels or removed_labels or config_changes)
